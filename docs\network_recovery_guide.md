# 网络断开重连卡顿问题解决方案

## 问题分析

根据您描述的现象，在Intel电脑上拔出网线一段时间重新插上后界面会卡顿，主要原因包括：

1. **Socket错误处理不完善** - 网络断开时socket进入异常状态，但没有重建连接
2. **线程池资源泄漏** - 异步操作使用的线程池在网络异常时可能泄漏
3. **响应队列堆积** - 旧的响应数据堆积影响后续处理
4. **定时器累积效应** - 多个高频定时器在网络异常时产生累积负载

## 解决方案

### 1. 改进的UDP Master

已对 `src/communication/udp_master.py` 进行以下改进：

#### Socket错误恢复机制
```python
def _handle_socket_error(self, error):
    """处理socket错误"""
    self._socket_error_count += 1
    if self._socket_error_count >= 5:
        logger.warning("Socket错误频繁，将在下次检查时尝试恢复")

def _recover_socket(self):
    """恢复socket连接"""
    # 关闭旧socket并重新创建
    # 重置错误计数
```

#### 专用线程池管理
```python
def _get_executor(self):
    """获取专用线程池执行器"""
    with self._executor_lock:
        if self._executor is None:
            self._executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=2, thread_name_prefix="UDP_Sender"
            )
        return self._executor
```

#### 网络断开清理
```python
def _cleanup_on_disconnect(self):
    """网络断开时的清理工作"""
    self.data_queue.clear()
    self._clear_response_queue()
    self.drop_count = 0
```

### 2. 优化的定时器管理

已对 `src/window/oscilloscope_frame.py` 进行优化：

#### 降低定时器频率
```python
# IPC数据接收定时器 - 从1ms改为5ms
self._ipc_timer.setInterval(5)

# 统计信息更新 - 从500ms改为1000ms  
self._stats_timer.setInterval(1000)
```

#### 限制处理时间
```python
def _receive_ipc_data(self):
    """接收IPC数据（优化版本）"""
    start_time = time.perf_counter()
    max_process_time = 0.003  # 最多处理3ms
    
    # 避免长时间阻塞
    for sample in samples:
        if time.perf_counter() - start_time > max_process_time:
            break
        # 处理数据...
```

### 3. 网络监控工具

新增 `src/utils/network_monitor.py` 提供：

#### 网络状态监控
```python
monitor = NetworkMonitor()
monitor.start_monitoring()

# 报告数据接收
monitor.report_data_received()

# 报告网络错误
monitor.report_network_error("Socket error")
```

#### 自动恢复机制
```python
def recovery_callback():
    # 触发socket恢复
    udp_master._recover_socket()

monitor.on_recovery_callback = recovery_callback
```

## 集成步骤

### 1. 更新主窗口

在 `src/window/main_window.py` 中集成网络监控：

```python
from src.utils.network_monitor import create_network_monitor_for_udp

class MainWindow:
    def __init__(self, cfg):
        # ... 现有代码 ...
        
        # 创建网络监控器
        self.network_monitor = create_network_monitor_for_udp(self.receiver)
        self.network_monitor.network_status_changed.connect(self._on_network_status_changed)
        self.network_monitor.start_monitoring()
    
    def _on_sample_received(self, fmt: int, values: list):
        """UDP采样数据回调"""
        # 报告数据接收
        self.network_monitor.report_data_received()
        
        # 原有处理逻辑...
        
    def _on_network_status_changed(self, is_healthy: bool):
        """网络状态变化处理"""
        if not is_healthy:
            logger.warning("检测到网络异常，可能影响界面响应")
        else:
            logger.info("网络状态恢复正常")
```

### 2. 更新UDP Master使用

确保在网络错误时正确报告：

```python
# 在UDP Master的错误处理中
def _on_data_received(self, data: bytes, addr: tuple):
    try:
        # 处理数据...
        if hasattr(self, 'network_monitor'):
            self.network_monitor.report_data_received()
    except Exception as e:
        if hasattr(self, 'network_monitor'):
            self.network_monitor.report_network_error(str(e))
        raise
```

## 配置建议

### 1. 网络超时设置
```python
# 在配置文件中调整超时参数
udp_master.online_timeout_ms = 1500  # 增加到1.5秒
udp_master._socket_recovery_timer.setInterval(3000)  # 3秒检查恢复
```

### 2. 线程池配置
```python
# 限制线程池大小，避免资源泄漏
max_workers = 2  # 足够处理发送任务
thread_name_prefix = "UDP_Sender"  # 便于调试
```

### 3. 队列大小调整
```python
# 根据实际情况调整队列大小
data_queue_maxlen = 3000  # 降低内存使用
resp_queue_maxsize = 1    # 保持单一响应
```

## 测试验证

### 1. 网络断开测试
1. 启动应用程序
2. 拔出网线等待10秒
3. 重新插入网线
4. 观察界面响应和日志输出

### 2. 长时间运行测试
1. 连续运行24小时
2. 定期检查内存使用情况
3. 监控线程数量变化

### 3. 错误恢复测试
1. 模拟网络错误
2. 验证自动恢复机制
3. 检查数据完整性

## 监控指标

### 1. 性能指标
- CPU使用率
- 内存使用量
- 线程数量
- 网络包丢失率

### 2. 网络指标
- 数据接收频率
- 错误发生次数
- 恢复执行次数
- 响应时间

### 3. 日志关键词
- "Socket错误频繁"
- "网络恢复执行"
- "网络状态异常"
- "线程池资源泄漏"

## 故障排除

### 1. 界面仍然卡顿
- 检查定时器频率设置
- 验证线程池是否正确关闭
- 查看队列是否有堆积

### 2. 网络恢复失败
- 检查socket权限
- 验证端口是否被占用
- 查看防火墙设置

### 3. 内存泄漏
- 监控线程池状态
- 检查队列清理
- 验证对象引用释放

通过这些改进，应该能够显著减少网络断开重连后的界面卡顿问题。
