# -*- coding: utf-8 -*-
"""
网络恢复机制测试脚本
用于验证网络断开重连后的恢复效果
"""

import asyncio
import logging
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.communication.udp_master import UDPMaster
from src.utils.network_monitor import NetworkMonitor, create_network_monitor_for_udp
from src.config.config_manager import ConfigManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("network_recovery_test.log", encoding="utf-8"),
    ],
)

logger = logging.getLogger(__name__)


class NetworkRecoveryTester:
    """网络恢复测试器"""
    
    def __init__(self):
        self.udp_master = None
        self.network_monitor = None
        self.test_running = False
        self.stats = {
            'data_received': 0,
            'errors_detected': 0,
            'recoveries_triggered': 0,
            'test_start_time': 0,
        }
        
    async def setup(self):
        """设置测试环境"""
        logger.info("设置测试环境...")
        
        # 创建UDP Master
        self.udp_master = UDPMaster(
            host="0.0.0.0",
            port=8888,
            on_sample=self._on_sample_received
        )
        
        # 创建网络监控器
        self.network_monitor = create_network_monitor_for_udp(self.udp_master)
        self.network_monitor.network_status_changed.connect(self._on_network_status_changed)
        self.network_monitor.network_recovery_needed.connect(self._on_recovery_needed)
        
        # 启动UDP Master
        await self.udp_master.start()
        
        # 启动网络监控
        self.network_monitor.start_monitoring()
        
        logger.info("测试环境设置完成")
        
    async def cleanup(self):
        """清理测试环境"""
        logger.info("清理测试环境...")
        
        if self.network_monitor:
            self.network_monitor.stop_monitoring()
            
        if self.udp_master:
            await self.udp_master.stop()
            
        logger.info("测试环境清理完成")
        
    def _on_sample_received(self, fmt: int, values: list):
        """数据接收回调"""
        self.stats['data_received'] += 1
        
        # 报告给网络监控器
        if self.network_monitor:
            self.network_monitor.report_data_received()
            
        # 每1000个数据包打印一次统计
        if self.stats['data_received'] % 1000 == 0:
            logger.info(f"已接收 {self.stats['data_received']} 个数据包")
            
    def _on_network_status_changed(self, is_healthy: bool):
        """网络状态变化回调"""
        if is_healthy:
            logger.info("✅ 网络状态正常")
        else:
            logger.warning("❌ 网络状态异常")
            self.stats['errors_detected'] += 1
            
    def _on_recovery_needed(self):
        """网络恢复需要回调"""
        logger.info("🔄 触发网络恢复")
        self.stats['recoveries_triggered'] += 1
        
    async def run_basic_test(self, duration_seconds=60):
        """运行基础测试"""
        logger.info(f"开始基础测试，持续 {duration_seconds} 秒...")
        
        self.test_running = True
        self.stats['test_start_time'] = time.time()
        
        try:
            # 等待指定时间
            await asyncio.sleep(duration_seconds)
            
        finally:
            self.test_running = False
            self._print_test_results()
            
    async def run_network_interruption_test(self):
        """运行网络中断测试"""
        logger.info("开始网络中断测试...")
        logger.info("请在10秒后拔出网线，等待20秒后重新插入")
        
        self.test_running = True
        self.stats['test_start_time'] = time.time()
        
        try:
            # 第一阶段：正常运行10秒
            logger.info("阶段1: 正常运行 10 秒")
            await asyncio.sleep(10)
            
            # 提示用户拔出网线
            logger.info("⚠️  请现在拔出网线...")
            await asyncio.sleep(2)
            
            # 第二阶段：网线断开20秒
            logger.info("阶段2: 网线断开 20 秒")
            for i in range(20):
                await asyncio.sleep(1)
                logger.info(f"网线断开中... {i+1}/20 秒")
                
            # 提示用户插入网线
            logger.info("⚠️  请现在插入网线...")
            await asyncio.sleep(2)
            
            # 第三阶段：网线恢复30秒
            logger.info("阶段3: 网线恢复 30 秒")
            for i in range(30):
                await asyncio.sleep(1)
                if i % 5 == 0:
                    logger.info(f"网线恢复中... {i+1}/30 秒")
                    
        finally:
            self.test_running = False
            self._print_test_results()
            
    def _print_test_results(self):
        """打印测试结果"""
        test_duration = time.time() - self.stats['test_start_time']
        
        logger.info("=" * 50)
        logger.info("测试结果统计")
        logger.info("=" * 50)
        logger.info(f"测试持续时间: {test_duration:.1f} 秒")
        logger.info(f"数据包接收数量: {self.stats['data_received']}")
        logger.info(f"网络错误检测次数: {self.stats['errors_detected']}")
        logger.info(f"网络恢复触发次数: {self.stats['recoveries_triggered']}")
        
        if test_duration > 0:
            data_rate = self.stats['data_received'] / test_duration
            logger.info(f"平均数据接收率: {data_rate:.1f} 包/秒")
            
        # 网络监控器统计
        if self.network_monitor:
            monitor_stats = self.network_monitor.get_statistics()
            logger.info("网络监控器统计:")
            for key, value in monitor_stats.items():
                logger.info(f"  {key}: {value}")
                
        logger.info("=" * 50)


async def main():
    """主函数"""
    tester = NetworkRecoveryTester()
    
    try:
        await tester.setup()
        
        # 选择测试类型
        print("\n请选择测试类型:")
        print("1. 基础测试 (60秒)")
        print("2. 网络中断测试 (手动拔插网线)")
        print("3. 长时间测试 (10分钟)")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            await tester.run_basic_test(60)
        elif choice == "2":
            await tester.run_network_interruption_test()
        elif choice == "3":
            await tester.run_basic_test(600)  # 10分钟
        else:
            logger.error("无效选择")
            return
            
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
