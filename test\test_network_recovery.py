# -*- coding: utf-8 -*-
"""
网络恢复机制测试脚本
用于验证网络断开重连后的恢复效果
"""

import asyncio
import logging
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.communication.udp_master import UDPMaster
from src.utils.network_monitor import NetworkMonitor, create_network_monitor_for_udp
from src.config.config_manager import ConfigManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("network_recovery_test.log", encoding="utf-8"),
    ],
)

logger = logging.getLogger(__name__)


class NetworkRecoveryTester:
    """网络恢复测试器 - 深度诊断版本"""

    def __init__(self):
        self.udp_master = None
        self.network_monitor = None
        self.test_running = False
        self.stats = {
            'data_received': 0,
            'errors_detected': 0,
            'recoveries_triggered': 0,
            'test_start_time': 0,
            'ui_response_times': [],
            'socket_operations': [],
            'thread_counts': [],
            'memory_usage': [],
        }

        # 系统信息收集
        self.system_info = self._collect_system_info()

        # GUI响应时间测试
        self.gui_test_timer = None
        self.last_gui_response = time.time()
        
    async def setup(self):
        """设置测试环境"""
        logger.info("设置测试环境...")
        
        # 创建UDP Master
        self.udp_master = UDPMaster(
            host="0.0.0.0",
            port=8888,
            on_sample=self._on_sample_received
        )
        
        # 创建网络监控器
        self.network_monitor = create_network_monitor_for_udp(self.udp_master)
        self.network_monitor.network_status_changed.connect(self._on_network_status_changed)
        self.network_monitor.network_recovery_needed.connect(self._on_recovery_needed)
        
        # 启动UDP Master
        await self.udp_master.start()
        
        # 启动网络监控
        self.network_monitor.start_monitoring()
        
        logger.info("测试环境设置完成")
        
    async def cleanup(self):
        """清理测试环境"""
        logger.info("清理测试环境...")
        
        if self.network_monitor:
            self.network_monitor.stop_monitoring()
            
        if self.udp_master:
            await self.udp_master.stop()
            
        logger.info("测试环境清理完成")

    def _collect_system_info(self):
        """收集系统信息"""
        import platform
        import psutil

        info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total,
            'network_interfaces': [],
        }

        # 网络接口信息
        try:
            import psutil
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = {'name': interface, 'addresses': []}
                for addr in addrs:
                    interface_info['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                    })
                info['network_interfaces'].append(interface_info)
        except Exception as e:
            logger.warning(f"无法获取网络接口信息: {e}")

        return info

    def _monitor_system_resources(self):
        """监控系统资源"""
        try:
            import psutil
            import threading

            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # 内存使用
            memory = psutil.virtual_memory()

            # 线程数量
            process = psutil.Process()
            thread_count = process.num_threads()

            # 网络统计
            net_io = psutil.net_io_counters()

            self.stats['thread_counts'].append(thread_count)
            self.stats['memory_usage'].append(memory.percent)

            # 记录异常情况
            if cpu_percent > 80:
                logger.warning(f"CPU使用率过高: {cpu_percent}%")
            if memory.percent > 80:
                logger.warning(f"内存使用率过高: {memory.percent}%")
            if thread_count > 50:
                logger.warning(f"线程数量过多: {thread_count}")

        except Exception as e:
            logger.error(f"系统资源监控失败: {e}")

    def _on_sample_received(self, fmt: int, values: list):
        """数据接收回调 - 增强诊断"""
        receive_time = time.time()
        self.stats['data_received'] += 1

        # 记录接收时间间隔
        if hasattr(self, '_last_receive_time'):
            interval = receive_time - self._last_receive_time
            if interval > 0.1:  # 超过100ms的间隔
                logger.warning(f"数据接收间隔异常: {interval:.3f}s")
        self._last_receive_time = receive_time

        # 报告给网络监控器
        if self.network_monitor:
            self.network_monitor.report_data_received()

        # 定期监控系统资源
        if self.stats['data_received'] % 1000 == 0:
            logger.info(f"已接收 {self.stats['data_received']} 个数据包")
            self._monitor_system_resources()

        # 模拟GUI操作响应时间测试
        if self.stats['data_received'] % 500 == 0:
            self._test_gui_response_time()

    def _test_gui_response_time(self):
        """测试GUI响应时间"""
        try:
            import threading
            import queue

            # 模拟GUI操作
            start_time = time.perf_counter()

            # 创建一个简单的线程任务来模拟GUI操作
            result_queue = queue.Queue()

            def gui_task():
                # 模拟一些GUI相关的操作
                time.sleep(0.001)  # 1ms的模拟操作
                result_queue.put(time.perf_counter())

            thread = threading.Thread(target=gui_task)
            thread.start()
            thread.join(timeout=0.1)  # 100ms超时

            if not result_queue.empty():
                end_time = result_queue.get()
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                self.stats['ui_response_times'].append(response_time)

                # 如果响应时间过长，记录警告
                if response_time > 50:  # 超过50ms
                    logger.warning(f"GUI响应时间异常: {response_time:.1f}ms")
            else:
                logger.error("GUI响应超时!")
                self.stats['ui_response_times'].append(999.9)  # 标记为超时

        except Exception as e:
            logger.error(f"GUI响应时间测试失败: {e}")

    def _test_socket_performance(self):
        """测试socket性能"""
        try:
            import socket

            # 测试socket创建时间
            start_time = time.perf_counter()
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            test_socket.close()
            socket_create_time = (time.perf_counter() - start_time) * 1000

            self.stats['socket_operations'].append({
                'operation': 'create_close',
                'time_ms': socket_create_time,
                'timestamp': time.time()
            })

            if socket_create_time > 10:  # 超过10ms
                logger.warning(f"Socket创建时间异常: {socket_create_time:.1f}ms")

        except Exception as e:
            logger.error(f"Socket性能测试失败: {e}")

    def _on_network_status_changed(self, is_healthy: bool):
        """网络状态变化回调 - 增强诊断"""
        status_time = time.time()

        if is_healthy:
            logger.info("✅ 网络状态正常")
            # 网络恢复时测试socket性能
            self._test_socket_performance()
        else:
            logger.warning("❌ 网络状态异常")
            self.stats['errors_detected'] += 1

            # 记录网络异常时的系统状态
            self._monitor_system_resources()

        # 记录状态变化的详细信息
        logger.info(f"网络状态变化时间: {time.strftime('%H:%M:%S', time.localtime(status_time))}")

    def _on_recovery_needed(self):
        """网络恢复需要回调 - 增强诊断"""
        recovery_time = time.time()
        logger.info("🔄 触发网络恢复")
        self.stats['recoveries_triggered'] += 1

        # 记录恢复前的系统状态
        logger.info(f"恢复触发时间: {time.strftime('%H:%M:%S', time.localtime(recovery_time))}")
        self._monitor_system_resources()
        self._test_socket_performance()
        
    async def run_basic_test(self, duration_seconds=60):
        """运行基础测试"""
        logger.info(f"开始基础测试，持续 {duration_seconds} 秒...")
        
        self.test_running = True
        self.stats['test_start_time'] = time.time()
        
        try:
            # 等待指定时间
            await asyncio.sleep(duration_seconds)
            
        finally:
            self.test_running = False
            self._print_test_results()
            
    async def run_network_interruption_test(self):
        """运行网络中断测试"""
        logger.info("开始网络中断测试...")
        logger.info("请在10秒后拔出网线，等待20秒后重新插入")
        
        self.test_running = True
        self.stats['test_start_time'] = time.time()
        
        try:
            # 第一阶段：正常运行10秒
            logger.info("阶段1: 正常运行 10 秒")
            await asyncio.sleep(10)
            
            # 提示用户拔出网线
            logger.info("⚠️  请现在拔出网线...")
            await asyncio.sleep(2)
            
            # 第二阶段：网线断开20秒
            logger.info("阶段2: 网线断开 20 秒")
            for i in range(20):
                await asyncio.sleep(1)
                logger.info(f"网线断开中... {i+1}/20 秒")
                
            # 提示用户插入网线
            logger.info("⚠️  请现在插入网线...")
            await asyncio.sleep(2)
            
            # 第三阶段：网线恢复30秒
            logger.info("阶段3: 网线恢复 30 秒")
            for i in range(30):
                await asyncio.sleep(1)
                if i % 5 == 0:
                    logger.info(f"网线恢复中... {i+1}/30 秒")
                    
        finally:
            self.test_running = False
            self._print_test_results()
            
    def _print_test_results(self):
        """打印测试结果"""
        test_duration = time.time() - self.stats['test_start_time']
        
        logger.info("=" * 50)
        logger.info("测试结果统计")
        logger.info("=" * 50)
        logger.info(f"测试持续时间: {test_duration:.1f} 秒")
        logger.info(f"数据包接收数量: {self.stats['data_received']}")
        logger.info(f"网络错误检测次数: {self.stats['errors_detected']}")
        logger.info(f"网络恢复触发次数: {self.stats['recoveries_triggered']}")
        
        if test_duration > 0:
            data_rate = self.stats['data_received'] / test_duration
            logger.info(f"平均数据接收率: {data_rate:.1f} 包/秒")
            
        # 网络监控器统计
        if self.network_monitor:
            monitor_stats = self.network_monitor.get_statistics()
            logger.info("网络监控器统计:")
            for key, value in monitor_stats.items():
                logger.info(f"  {key}: {value}")
                
        logger.info("=" * 50)


async def main():
    """主函数"""
    tester = NetworkRecoveryTester()
    
    try:
        await tester.setup()
        
        # 选择测试类型
        print("\n请选择测试类型:")
        print("1. 基础测试 (60秒)")
        print("2. 网络中断测试 (手动拔插网线)")
        print("3. 长时间测试 (10分钟)")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            await tester.run_basic_test(60)
        elif choice == "2":
            await tester.run_network_interruption_test()
        elif choice == "3":
            await tester.run_basic_test(600)  # 10分钟
        else:
            logger.error("无效选择")
            return
            
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
