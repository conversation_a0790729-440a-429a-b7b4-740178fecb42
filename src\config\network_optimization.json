{"network_optimization": {"description": "网络优化配置，用于解决网络断开重连后的卡顿问题", "udp_settings": {"receive_buffer_size": 52428800, "send_buffer_size": 1048576, "socket_timeout_ms": 10, "max_packet_size": 65536, "queue_max_length": 3000, "batch_process_size": 200}, "error_recovery": {"max_socket_errors": 5, "socket_recovery_interval_ms": 3000, "online_timeout_ms": 1500, "response_timeout_ms": 1000, "recovery_retry_count": 3}, "timer_optimization": {"ipc_receive_interval_ms": 5, "plot_refresh_rate_hz": 30, "stats_update_interval_ms": 1000, "network_monitor_interval_ms": 2000, "online_check_interval_ms": 100}, "thread_pool": {"max_workers": 2, "thread_name_prefix": "UDP_Sender", "shutdown_wait_timeout": 5.0}, "performance_limits": {"max_ipc_process_time_ms": 3, "max_batch_process_count": 10000, "queue_cleanup_threshold": 1000, "memory_cleanup_interval_ms": 30000}, "monitoring": {"enable_network_monitor": true, "data_timeout_ms": 2000, "max_error_count": 5, "recovery_interval_ms": 10000, "statistics_log_interval_ms": 60000}, "intel_specific": {"description": "Intel平台特定优化", "enable_intel_optimizations": true, "reduce_timer_frequency": true, "aggressive_cleanup": true, "socket_keepalive": true, "tcp_nodelay": true}, "debug_settings": {"enable_performance_logging": false, "log_socket_errors": true, "log_recovery_attempts": true, "log_queue_status": false, "detailed_network_stats": false}}}