# -*- coding: utf-8 -*-
"""
Intel平台特定问题诊断工具
专门用于诊断Intel电脑上网络断开重连后界面卡顿的问题
"""

import time
import logging
import platform
import subprocess
import sys
import os
import threading
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("intel_diagnosis.log", encoding="utf-8"),
    ],
)

logger = logging.getLogger(__name__)


class IntelPlatformDiagnostic:
    """Intel平台诊断器"""
    
    def __init__(self):
        self.system_info = {}
        self.network_adapters = []
        self.driver_info = []
        self.performance_baseline = {}
        self.test_results = {}
        
    def collect_system_info(self):
        """收集系统信息"""
        logger.info("收集系统信息...")
        
        self.system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'machine': platform.machine(),
            'python_version': platform.python_version(),
            'windows_version': platform.win32_ver() if sys.platform == 'win32' else None,
        }
        
        # 检查是否为Intel处理器
        processor_info = platform.processor().lower()
        self.system_info['is_intel'] = 'intel' in processor_info
        
        logger.info(f"处理器: {self.system_info['processor']}")
        logger.info(f"是否Intel处理器: {self.system_info['is_intel']}")
        
    def collect_network_info(self):
        """收集网络适配器信息"""
        logger.info("收集网络适配器信息...")
        
        try:
            # 使用wmic获取网络适配器信息
            if sys.platform == 'win32':
                self._collect_windows_network_info()
            else:
                logger.warning("非Windows平台，跳过网络适配器详细信息收集")
                
        except Exception as e:
            logger.error(f"收集网络信息失败: {e}")
    
    def _collect_windows_network_info(self):
        """收集Windows网络信息"""
        try:
            # 获取网络适配器信息
            cmd = 'wmic path win32_networkadapter get name,manufacturer,description,netconnectionstatus /format:csv'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='gbk')
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                for line in lines:
                    if line.strip():
                        parts = line.split(',')
                        if len(parts) >= 4:
                            adapter_info = {
                                'name': parts[2] if len(parts) > 2 else '',
                                'manufacturer': parts[3] if len(parts) > 3 else '',
                                'description': parts[1] if len(parts) > 1 else '',
                                'status': parts[4] if len(parts) > 4 else '',
                            }
                            self.network_adapters.append(adapter_info)
                            
                            # 检查Intel网络适配器
                            if 'intel' in adapter_info['manufacturer'].lower():
                                logger.info(f"发现Intel网络适配器: {adapter_info['name']}")
                                
            # 获取网络驱动信息
            cmd = 'wmic path win32_systemdriver where "name like \'%net%\'" get name,version,pathname /format:csv'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='gbk')
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                for line in lines:
                    if line.strip():
                        parts = line.split(',')
                        if len(parts) >= 3:
                            driver_info = {
                                'name': parts[1] if len(parts) > 1 else '',
                                'version': parts[3] if len(parts) > 3 else '',
                                'path': parts[2] if len(parts) > 2 else '',
                            }
                            self.driver_info.append(driver_info)
                            
        except Exception as e:
            logger.error(f"收集Windows网络信息失败: {e}")
    
    def test_socket_performance(self):
        """测试socket性能"""
        logger.info("测试socket性能...")
        
        import socket
        
        results = {
            'create_times': [],
            'bind_times': [],
            'close_times': [],
            'total_operations': 0,
        }
        
        # 测试多次socket操作
        for i in range(100):
            try:
                # 测试socket创建时间
                start_time = time.perf_counter()
                test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                create_time = (time.perf_counter() - start_time) * 1000
                results['create_times'].append(create_time)
                
                # 测试bind时间
                start_time = time.perf_counter()
                test_socket.bind(('127.0.0.1', 0))  # 绑定到任意可用端口
                bind_time = (time.perf_counter() - start_time) * 1000
                results['bind_times'].append(bind_time)
                
                # 测试close时间
                start_time = time.perf_counter()
                test_socket.close()
                close_time = (time.perf_counter() - start_time) * 1000
                results['close_times'].append(close_time)
                
                results['total_operations'] += 1
                
            except Exception as e:
                logger.error(f"Socket操作 {i} 失败: {e}")
                
        # 计算统计信息
        if results['create_times']:
            results['avg_create_time'] = sum(results['create_times']) / len(results['create_times'])
            results['max_create_time'] = max(results['create_times'])
            results['avg_bind_time'] = sum(results['bind_times']) / len(results['bind_times'])
            results['max_bind_time'] = max(results['bind_times'])
            results['avg_close_time'] = sum(results['close_times']) / len(results['close_times'])
            results['max_close_time'] = max(results['close_times'])
            
            logger.info(f"Socket创建平均时间: {results['avg_create_time']:.2f}ms")
            logger.info(f"Socket绑定平均时间: {results['avg_bind_time']:.2f}ms")
            logger.info(f"Socket关闭平均时间: {results['avg_close_time']:.2f}ms")
            
            # 检查异常情况
            if results['max_create_time'] > 10:
                logger.warning(f"Socket创建时间异常: 最大 {results['max_create_time']:.2f}ms")
            if results['max_bind_time'] > 10:
                logger.warning(f"Socket绑定时间异常: 最大 {results['max_bind_time']:.2f}ms")
                
        self.test_results['socket_performance'] = results
    
    def test_thread_performance(self):
        """测试线程性能"""
        logger.info("测试线程性能...")
        
        results = {
            'create_times': [],
            'join_times': [],
            'total_threads': 0,
        }
        
        # 测试线程创建和销毁性能
        for i in range(50):
            try:
                def dummy_task():
                    time.sleep(0.001)  # 1ms任务
                
                # 测试线程创建时间
                start_time = time.perf_counter()
                thread = threading.Thread(target=dummy_task)
                thread.start()
                create_time = (time.perf_counter() - start_time) * 1000
                results['create_times'].append(create_time)
                
                # 测试线程join时间
                start_time = time.perf_counter()
                thread.join()
                join_time = (time.perf_counter() - start_time) * 1000
                results['join_times'].append(join_time)
                
                results['total_threads'] += 1
                
            except Exception as e:
                logger.error(f"线程操作 {i} 失败: {e}")
        
        # 计算统计信息
        if results['create_times']:
            results['avg_create_time'] = sum(results['create_times']) / len(results['create_times'])
            results['max_create_time'] = max(results['create_times'])
            results['avg_join_time'] = sum(results['join_times']) / len(results['join_times'])
            results['max_join_time'] = max(results['join_times'])
            
            logger.info(f"线程创建平均时间: {results['avg_create_time']:.2f}ms")
            logger.info(f"线程join平均时间: {results['avg_join_time']:.2f}ms")
            
            # 检查异常情况
            if results['max_create_time'] > 5:
                logger.warning(f"线程创建时间异常: 最大 {results['max_create_time']:.2f}ms")
            if results['max_join_time'] > 10:
                logger.warning(f"线程join时间异常: 最大 {results['max_join_time']:.2f}ms")
                
        self.test_results['thread_performance'] = results
    
    def check_intel_specific_issues(self):
        """检查Intel特定问题"""
        logger.info("检查Intel特定问题...")
        
        issues_found = []
        
        # 检查是否为Intel处理器
        if not self.system_info.get('is_intel', False):
            logger.info("非Intel处理器，跳过Intel特定检查")
            return issues_found
        
        # 检查Intel网络适配器
        intel_adapters = [adapter for adapter in self.network_adapters 
                         if 'intel' in adapter.get('manufacturer', '').lower()]
        
        if intel_adapters:
            logger.info(f"发现 {len(intel_adapters)} 个Intel网络适配器")
            for adapter in intel_adapters:
                logger.info(f"  - {adapter.get('name', 'Unknown')}")
                
                # 检查已知问题
                adapter_name = adapter.get('name', '').lower()
                if 'i219' in adapter_name:
                    issues_found.append("Intel I219网络适配器可能存在驱动兼容性问题")
                if 'wireless' in adapter_name and 'ac' in adapter_name:
                    issues_found.append("Intel AC无线网卡可能存在电源管理问题")
        
        # 检查Windows版本兼容性
        if self.system_info.get('windows_version'):
            win_version = self.system_info['windows_version'][0]
            if win_version == '10':
                issues_found.append("Windows 10可能存在Intel网络驱动兼容性问题")
        
        self.test_results['intel_issues'] = issues_found
        return issues_found
    
    def generate_report(self):
        """生成诊断报告"""
        logger.info("生成诊断报告...")
        
        report = []
        report.append("=" * 60)
        report.append("Intel平台网络问题诊断报告")
        report.append("=" * 60)
        
        # 系统信息
        report.append("\n【系统信息】")
        for key, value in self.system_info.items():
            report.append(f"  {key}: {value}")
        
        # 网络适配器
        report.append("\n【网络适配器】")
        for adapter in self.network_adapters:
            if adapter.get('name'):
                report.append(f"  - {adapter['name']} ({adapter.get('manufacturer', 'Unknown')})")
        
        # 性能测试结果
        if 'socket_performance' in self.test_results:
            socket_perf = self.test_results['socket_performance']
            report.append("\n【Socket性能】")
            report.append(f"  平均创建时间: {socket_perf.get('avg_create_time', 0):.2f}ms")
            report.append(f"  最大创建时间: {socket_perf.get('max_create_time', 0):.2f}ms")
            report.append(f"  平均绑定时间: {socket_perf.get('avg_bind_time', 0):.2f}ms")
        
        if 'thread_performance' in self.test_results:
            thread_perf = self.test_results['thread_performance']
            report.append("\n【线程性能】")
            report.append(f"  平均创建时间: {thread_perf.get('avg_create_time', 0):.2f}ms")
            report.append(f"  最大创建时间: {thread_perf.get('max_create_time', 0):.2f}ms")
        
        # Intel特定问题
        if 'intel_issues' in self.test_results:
            issues = self.test_results['intel_issues']
            report.append("\n【Intel特定问题】")
            if issues:
                for issue in issues:
                    report.append(f"  ⚠️  {issue}")
            else:
                report.append("  ✅ 未发现已知Intel特定问题")
        
        report.append("\n" + "=" * 60)
        
        # 输出报告
        report_text = "\n".join(report)
        logger.info(f"\n{report_text}")
        
        # 保存到文件
        with open("intel_diagnosis_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        return report_text
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("开始Intel平台完整诊断...")
        
        try:
            self.collect_system_info()
            self.collect_network_info()
            self.test_socket_performance()
            self.test_thread_performance()
            self.check_intel_specific_issues()
            
            report = self.generate_report()
            logger.info("诊断完成，报告已保存到 intel_diagnosis_report.txt")
            
            return report
            
        except Exception as e:
            logger.error(f"诊断过程中出现错误: {e}")
            return None


def main():
    """主函数"""
    print("Intel平台网络问题诊断工具")
    print("=" * 40)
    
    diagnostic = IntelPlatformDiagnostic()
    diagnostic.run_full_diagnosis()
    
    print("\n诊断完成！请查看生成的报告文件。")


if __name__ == "__main__":
    main()
