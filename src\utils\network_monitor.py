# -*- coding: utf-8 -*-
"""
网络状态监控和恢复工具
用于检测网络断开重连后的问题并提供恢复机制
"""

import logging
import time
import threading
from typing import Optional, Callable
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)


class NetworkMonitor(QObject):
    """网络状态监控器"""
    
    # 信号
    network_status_changed = pyqtSignal(bool)  # 网络状态变化
    network_recovery_needed = pyqtSignal()    # 需要网络恢复
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 监控状态
        self.is_monitoring = False
        self.network_healthy = True
        
        # 统计信息
        self.last_data_time = 0
        self.data_count = 0
        self.error_count = 0
        self.recovery_count = 0
        
        # 配置参数
        self.data_timeout_ms = 2000  # 2秒无数据认为异常
        self.max_error_count = 5     # 最大错误次数
        self.recovery_interval_ms = 10000  # 10秒恢复间隔
        
        # 定时器
        self._monitor_timer = QTimer(self)
        self._monitor_timer.setInterval(1000)  # 1秒检查一次
        self._monitor_timer.timeout.connect(self._check_network_status)
        
        # 恢复定时器
        self._recovery_timer = QTimer(self)
        self._recovery_timer.setInterval(self.recovery_interval_ms)
        self._recovery_timer.timeout.connect(self._trigger_recovery)
        self._recovery_timer.setSingleShot(True)
        
        # 回调函数
        self.on_recovery_callback: Optional[Callable] = None
        
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.network_healthy = True
        self.error_count = 0
        self.last_data_time = time.time() * 1000
        
        self._monitor_timer.start()
        logger.info("网络监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        self._monitor_timer.stop()
        self._recovery_timer.stop()
        logger.info("网络监控已停止")
        
    def report_data_received(self):
        """报告收到数据"""
        self.last_data_time = time.time() * 1000
        self.data_count += 1
        
        # 如果之前有错误，重置错误计数
        if self.error_count > 0:
            self.error_count = 0
            logger.debug("网络错误计数已重置")
            
    def report_network_error(self, error_msg: str = ""):
        """报告网络错误"""
        self.error_count += 1
        logger.warning(f"网络错误 (第{self.error_count}次): {error_msg}")
        
        # 如果错误次数过多，触发恢复
        if self.error_count >= self.max_error_count:
            self._schedule_recovery()
            
    def _check_network_status(self):
        """检查网络状态"""
        if not self.is_monitoring:
            return
            
        current_time = time.time() * 1000
        time_since_last_data = current_time - self.last_data_time
        
        # 检查数据超时
        is_healthy = time_since_last_data < self.data_timeout_ms
        
        # 状态变化时发送信号
        if is_healthy != self.network_healthy:
            self.network_healthy = is_healthy
            self.network_status_changed.emit(is_healthy)
            
            if not is_healthy:
                logger.warning(f"网络状态异常: {time_since_last_data:.0f}ms无数据")
                self._schedule_recovery()
            else:
                logger.info("网络状态恢复正常")
                self._recovery_timer.stop()
                
    def _schedule_recovery(self):
        """安排网络恢复"""
        if not self._recovery_timer.isActive():
            logger.info(f"安排网络恢复，{self.recovery_interval_ms}ms后执行")
            self._recovery_timer.start()
            
    def _trigger_recovery(self):
        """触发网络恢复"""
        self.recovery_count += 1
        logger.info(f"触发网络恢复 (第{self.recovery_count}次)")
        
        # 发送恢复信号
        self.network_recovery_needed.emit()
        
        # 调用回调函数
        if self.on_recovery_callback:
            try:
                self.on_recovery_callback()
            except Exception as e:
                logger.error(f"网络恢复回调失败: {e}")
                
    def get_statistics(self) -> dict:
        """获取统计信息"""
        return {
            'is_monitoring': self.is_monitoring,
            'network_healthy': self.network_healthy,
            'data_count': self.data_count,
            'error_count': self.error_count,
            'recovery_count': self.recovery_count,
            'last_data_time': self.last_data_time,
        }
        
    def reset_statistics(self):
        """重置统计信息"""
        self.data_count = 0
        self.error_count = 0
        self.recovery_count = 0
        self.last_data_time = time.time() * 1000
        logger.info("网络监控统计信息已重置")


class NetworkRecoveryManager:
    """网络恢复管理器"""
    
    def __init__(self):
        self.recovery_strategies = []
        self.recovery_lock = threading.Lock()
        
    def add_recovery_strategy(self, strategy: Callable):
        """添加恢复策略"""
        with self.recovery_lock:
            self.recovery_strategies.append(strategy)
            
    def execute_recovery(self):
        """执行网络恢复"""
        with self.recovery_lock:
            logger.info(f"开始执行网络恢复，共{len(self.recovery_strategies)}个策略")
            
            for i, strategy in enumerate(self.recovery_strategies):
                try:
                    logger.info(f"执行恢复策略 {i+1}/{len(self.recovery_strategies)}")
                    strategy()
                except Exception as e:
                    logger.error(f"恢复策略 {i+1} 执行失败: {e}")
                    
            logger.info("网络恢复执行完成")


def create_network_monitor_for_udp(udp_master) -> NetworkMonitor:
    """为UDP Master创建网络监控器"""
    monitor = NetworkMonitor()
    
    # 设置恢复回调
    def recovery_callback():
        try:
            # 触发UDP Master的socket恢复
            if hasattr(udp_master, '_recover_socket'):
                udp_master._recover_socket()
        except Exception as e:
            logger.error(f"UDP恢复失败: {e}")
            
    monitor.on_recovery_callback = recovery_callback
    
    return monitor
